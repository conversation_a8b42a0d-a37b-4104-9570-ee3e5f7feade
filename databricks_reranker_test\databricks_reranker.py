"""
Simple script to test the Databricks reranker model.
This script sends query-document pairs to the Databricks reranker model and prints the reranking scores.
"""

import sys
import json
import pandas as pd
import requests
import os
from typing import List, <PERSON>ple
from transformers import AutoTokenizer

# Databricks endpoint configuration
DATABRICKS_BASE_URL = "https://int.api.worldbank.org/portfoliointelligence/serving-endpoints"
DATABRICKS_RERANKER_MODEL_NAME = "cpm-marco-ms"
DATABRICKS_RERANKER_ENDPOINT = f"{DATABRICKS_BASE_URL}/{DATABRICKS_RERANKER_MODEL_NAME}/invocations"

# Maximum token length for the reranker model
MAX_TOKEN_LENGTH = 512

# Load BERT tokenizer for accurate token counting
def get_tokenizer():
    """
    Load the BERT tokenizer for token counting.

    Returns:
        A BERT tokenizer instance or None if loading fails
    """
    try:
        # Use a BERT-based tokenizer since the reranker is likely BERT-based
        tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
        print("BERT tokenizer loaded successfully for token counting")
        return tokenizer
    except Exception as e:
        print(f"Error loading BERT tokenizer: {e}")
        print("Falling back to character-based truncation")
        return None

def get_databricks_token():
    """
    Gets the Databricks token from user input.

    Returns:
        The Databricks token or None if not available
    """
    # Always prompt the user for the token
    print("Please enter your Databricks API token:")
    databricks_token = input("Token: ").strip()

    if not databricks_token:
        print("Error: No token provided. Cannot proceed without a valid Databricks API token.")
        return None

    return databricks_token

# Initialize tokenizer
tokenizer = get_tokenizer()

def _truncate_text_pair(query: str, document: str) -> Tuple[str, str]:
    """
    Truncate a query-document pair to fit within the model's maximum token length.

    This method uses a tokenizer to accurately count tokens and ensures the combined
    length of the query and document doesn't exceed the model's maximum token length.

    Args:
        query: The query text
        document: The document text

    Returns:
        Tuple of (truncated_query, truncated_document)
    """
    global tokenizer

    if not tokenizer:
        # Fall back to simple character-based truncation if tokenizer isn't available
        print("Tokenizer not available, falling back to character-based truncation")
        truncated_query = _truncate_by_chars(query, 300)
        truncated_document = _truncate_by_chars(document, 1200)
        return truncated_query, truncated_document

    # Get token counts
    query_tokens = tokenizer.encode(query, add_special_tokens=False)
    doc_tokens = tokenizer.encode(document, add_special_tokens=False)

    # Account for special tokens ([CLS], [SEP], [SEP]) = 3 tokens
    special_tokens_count = 3
    max_total_tokens = MAX_TOKEN_LENGTH - special_tokens_count

    # If the combined length is already within limits, return the original texts
    if len(query_tokens) + len(doc_tokens) <= max_total_tokens:
        return query, document

    # Allocate tokens proportionally, but ensure query gets at least 10% of tokens
    # and at most 30% of tokens (to preserve more of the document context)
    query_ratio = min(0.3, max(0.1, len(query_tokens) / (len(query_tokens) + len(doc_tokens))))
    max_query_tokens = max(10, int(max_total_tokens * query_ratio))
    max_doc_tokens = max_total_tokens - max_query_tokens

    # Truncate if needed
    if len(query_tokens) > max_query_tokens:
        print(f"Query truncated from {len(query_tokens)} to {max_query_tokens} tokens")
        query_tokens = query_tokens[:max_query_tokens]

    if len(doc_tokens) > max_doc_tokens:
        print(f"Document truncated from {len(doc_tokens)} to {max_doc_tokens} tokens")
        doc_tokens = doc_tokens[:max_doc_tokens]

    # Decode back to text
    truncated_query = tokenizer.decode(query_tokens, skip_special_tokens=True)
    truncated_document = tokenizer.decode(doc_tokens, skip_special_tokens=True)

    return truncated_query, truncated_document

def _truncate_by_chars(text: str, max_chars: int = 1500) -> str:
    """
    Fallback method to truncate text by character count when tokenizer is unavailable.

    Args:
        text: The text to truncate
        max_chars: Maximum number of characters

    Returns:
        Truncated text
    """
    if len(text) <= max_chars:
        return text

    # Simple truncation - take the first max_chars characters
    truncated = text[:max_chars]

    # Try to truncate at a sentence or word boundary if possible
    last_period = truncated.rfind('.')
    if last_period > max_chars * 0.8:  # Only use period if it's not too far back
        return truncated[:last_period + 1]

    last_space = truncated.rfind(' ')
    if last_space > 0:
        return truncated[:last_space]

    return truncated

def predict(token, sentence_pairs):
    """
    Sends query-document pairs to the Databricks reranker model and returns scores.

    Args:
        token: Databricks API token
        sentence_pairs: List of [query, document] pairs to rerank

    Returns:
        List of scores for each pair
    """
    try:
        # Convert sentence pairs to DataFrame format expected by the model
        data = []
        for query, document in sentence_pairs:
            # Truncate both query and document to fit within model's token limit
            truncated_query, truncated_document = _truncate_text_pair(query, document)

            data.append({
                'text': truncated_query,
                'text_pair': truncated_document
            })

        df = pd.DataFrame(data)

        # Create the request payload
        ds_dict = {'dataframe_split': df.to_dict(orient='split')}
        data_json = json.dumps(ds_dict, allow_nan=True)

        # Set up headers
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        # Make the API call
        print(f"Calling Databricks reranker API with {len(sentence_pairs)} pairs")
        print(f"Endpoint URL: {DATABRICKS_RERANKER_ENDPOINT}")

        # Debug: Print the first part of the request payload
        print(f"Request payload sample: {data_json[:200]}...")

        response = requests.post(
            url=DATABRICKS_RERANKER_ENDPOINT,
            headers=headers,
            data=data_json
        )

        # Check for successful response
        if response.status_code == 200:
            print("API call successful (status code 200)")

            # Debug: Print the raw response
            response_text = response.text
            print(f"Raw response (first 200 chars): {response_text[:200]}...")

            # Parse the response
            try:
                result = response.json()
                print(f"Response JSON keys: {list(result.keys())}")
            except Exception as e:
                print(f"Error parsing JSON response: {e}")
                print(f"Full response text: {response_text}")
                return [0.0] * len(sentence_pairs)

            # Extract scores from the response
            if 'predictions' in result:
                predictions = result['predictions']
                print(f"Prediction format: {type(predictions).__name__}")

                # Handle the case where predictions is a list of dictionaries with 'score' field
                if isinstance(predictions, list) and all(isinstance(p, dict) for p in predictions):
                    print(f"Sample prediction item: {predictions[0] if predictions else 'empty'}")

                    # Extract just the scores from the dictionaries
                    if all('score' in p for p in predictions):
                        scores = [float(p['score']) for p in predictions]
                        print(f"Extracted scores: {scores}")
                        return scores
                    else:
                        print("Error: 'score' field not found in all prediction items")
                        # Try to extract whatever we can
                        scores = []
                        for i, p in enumerate(predictions):
                            if 'score' in p:
                                scores.append(float(p['score']))
                            else:
                                print(f"Warning: No score in prediction {i}: {p}")
                                scores.append(0.0)
                        return scores
                else:
                    # Try to handle as a simple list of scores
                    try:
                        scores = [float(score) for score in predictions]
                        return scores
                    except Exception as e:
                        print(f"Error converting predictions to scores: {e}")
                        print(f"Predictions: {predictions}")
                        return [0.0] * len(sentence_pairs)
            else:
                print("Error: Unexpected response format. 'predictions' key not found.")
                print(f"Response keys: {list(result.keys())}")
                return [0.0] * len(sentence_pairs)
        else:
            print(f"Error: API request failed with status code {response.status_code}")
            print(f"Response: {response.text}")
            return [0.0] * len(sentence_pairs)

    except Exception as e:
        print(f"Error in reranking: {e}")
        return [0.0] * len(sentence_pairs)

def main():
    """Main function to test the Databricks reranker model."""
    # Get Databricks token
    databricks_token = get_databricks_token()

    if not databricks_token:
        sys.exit(1)

    try:
        print("Testing Databricks reranker model...")

        # Test query-document pairs
        # Format: [query, document]
        test_pairs = [
            # Highly relevant pair
            ["What is the capital of France?", "Paris is the capital and most populous city of France."],

            # Somewhat relevant pair
            ["What is the capital of France?", "France is a country in Western Europe with several overseas territories."],

            # Irrelevant pair
            ["What is the capital of France?", "Berlin is the capital and largest city of Germany."],

            # Another highly relevant pair
            ["How does photosynthesis work?", "Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with the help of chlorophyll."],

            # Somewhat relevant pair
            ["How does photosynthesis work?", "Plants are essential for maintaining oxygen levels in the atmosphere through various biological processes."],

            # Irrelevant pair
            ["How does photosynthesis work?", "Cellular respiration is the process by which organisms break down glucose to release energy."]
        ]

        # Get reranking scores
        print(f"Sending {len(test_pairs)} query-document pairs to the reranker...")
        scores = predict(databricks_token, test_pairs)

        # Print results
        print("\nReranking Results:")
        print("-" * 50)

        for i, ((query, document), score) in enumerate(zip(test_pairs, scores)):
            print(f"\nPair {i+1}:")
            print(f"Query: {query}")
            print(f"Document: {document}")
            # Handle different score formats
            try:
                if isinstance(score, (int, float)):
                    print(f"Score: {float(score):.4f}")
                else:
                    print(f"Score: {score} (raw format)")
            except Exception as e:
                print(f"Score: {score} (could not format: {str(e)})")

        # Analyze results
        print("\n" + "-" * 50)
        print("Analysis:")

        # Convert all scores to floats for comparison
        try:
            float_scores = [float(score) if isinstance(score, (int, float)) else 0.0 for score in scores]

            # Check if the model ranks relevant documents higher
            france_scores = float_scores[:3]  # First three pairs are about France
            if len(france_scores) == 3 and france_scores[0] > france_scores[1] > france_scores[2]:
                print("✓ Test PASSED: Model correctly ranked the France-related documents (highly relevant > somewhat relevant > irrelevant)")
            else:
                print("✗ Test FAILED: Model did not correctly rank the France-related documents")
                print(f"  France scores: {france_scores}")

            if len(float_scores) >= 6:  # Make sure we have enough scores
                photosynthesis_scores = float_scores[3:6]  # Last three pairs are about photosynthesis
                if photosynthesis_scores[0] > photosynthesis_scores[1] > photosynthesis_scores[2]:
                    print("✓ Test PASSED: Model correctly ranked the photosynthesis-related documents (highly relevant > somewhat relevant > irrelevant)")
                else:
                    print("✗ Test FAILED: Model did not correctly rank the photosynthesis-related documents")
                    print(f"  Photosynthesis scores: {photosynthesis_scores}")

            # Overall test result
            if all(score > 0 for score in float_scores):
                print("\nOverall: Databricks reranker model is working correctly!")
            else:
                print("\nOverall: Some issues detected with the Databricks reranker model.")

        except Exception as e:
            print(f"Could not analyze scores due to formatting error: {str(e)}")
            print(f"Raw scores: {scores}")

    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
